using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using AwhGameServer.Infrastructure.Configurations;
using AwhGameServer.Infrastructure.Persistence.Ef.Seed;

namespace AwhGameServer.Infrastructure.Persistence.Ef.Hosting;

/// <summary>
/// Фоновый сервис для инициализации базы данных игровых данных при запуске приложения.
/// Создает БД если она не существует и заполняет её начальными данными.
/// </summary>
/// 
/// <param name="serviceProvider">Провайдер сервисов для создания области видимости.</param>
/// <param name="gameDataDbConfig">Конфигурация базы данных игровых данных.</param>
/// <param name="logger">Логгер для записи информации о процессе инициализации.</param>
public class GameDataDbInitializationHostedService(
    IServiceProvider serviceProvider,
    IOptions<GameDataDbConfig> gameDataDbConfig,
    ILogger<GameDataDbInitializationHostedService> logger)
    : IHostedService
{
    /// <summary>
    /// Выполняется при запуске приложения.
    /// Создает базу данных если она не существует и заполняет её начальными данными.
    /// </summary>
    public async Task StartAsync(CancellationToken cancellationToken)
    {
        logger.LogInformation("Начало инициализации базы игровых данных");

        using var scope = serviceProvider.CreateScope();

        var db = scope.ServiceProvider.GetRequiredService<GameDataDbContext>();

        var justCreated = await db.Database.EnsureCreatedAsync(cancellationToken);

        if (justCreated)
        {
            logger.LogInformation("База игровых данных создана. Начинается заполнение начальными данными");
            await GameDataDbSeeder.Seed(db, gameDataDbConfig.Value, cancellationToken);
            logger.LogInformation("База игровых данных успешно заполнена начальными данными");
        }
        else
        {
            logger.LogInformation("База игровых данных уже существует, заполнение начальными данными пропущено");
        }

        logger.LogInformation("Инициализация базы игровых данных завершена");
    }

    /// <summary>
    /// Выполняется при остановке приложения. Не требует дополнительных действий.
    /// </summary>
    public Task StopAsync(CancellationToken cancellationToken) => Task.CompletedTask;
}
