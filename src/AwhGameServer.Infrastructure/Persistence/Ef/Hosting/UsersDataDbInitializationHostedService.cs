using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace AwhGameServer.Infrastructure.Persistence.Ef.Hosting;

/// <summary>
/// Фоновый сервис для инициализации базы данных пользовательских данных при запуске приложения.
/// Создает БД если она не существует.
/// </summary>
/// 
/// <param name="serviceProvider">Провайдер сервисов для создания области видимости.</param>
/// <param name="logger">Логгер для записи информации о процессе инициализации.</param>
public class UsersDataDbInitializationHostedService(
    IServiceProvider serviceProvider, 
    ILogger<UsersDataDbInitializationHostedService> logger) 
    : IHostedService
{
    /// <summary>
    /// Выполняется при запуске приложения.
    /// Создает базу данных пользовательских данных если она не существует.
    /// </summary>
    public async Task StartAsync(CancellationToken cancellationToken)
    {
        logger.LogInformation("Начало инициализации базы пользовательских данных");

        using var scope = serviceProvider.CreateScope();

        var db = scope.ServiceProvider.GetRequiredService<UsersDataDbContext>();

        var justCreated = await db.Database.EnsureCreatedAsync(cancellationToken);

        logger.LogInformation(message: justCreated
            ? "База пользовательских данных успешно создана"
            : "База пользовательских данных уже существует");

        logger.LogInformation("Инициализация базы пользовательских данных завершена");
    }

    /// <summary>
    /// Выполняется при остановке приложения. Не требует дополнительных действий.
    /// </summary>
    public Task StopAsync(CancellationToken cancellationToken) => Task.CompletedTask;
}
