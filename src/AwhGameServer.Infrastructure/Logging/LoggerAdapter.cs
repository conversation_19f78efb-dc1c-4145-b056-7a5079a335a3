using Microsoft.Extensions.Logging;
using AwhGameServer.Application.Abstractions.Logging;

namespace AwhGameServer.Infrastructure.Logging;

/// <summary>
/// Адаптер для преобразования ILogger в IAppLogger.
/// </summary>
/// <param name="logger"></param>
/// <typeparam name="T"></typeparam>
internal sealed class LoggerAdapter<T>(ILogger<T> logger) : IAppLogger<T>
{
    public void Trace(string msg, params object?[] args) => logger.LogTrace(msg, args);
    public void Debug(string msg, params object?[] args) => logger.LogDebug(msg, args);
    public void Info (string msg, params object?[] args) => logger.LogInformation(msg, args);
    public void Warn (string msg, params object?[] args) => logger.LogWarning(msg, args);
    public void Error(Exception ex, string msg, params object?[] args) => logger.LogError(ex, msg, args);
    public void Critical(Exception ex, string msg, params object?[] args) => logger.LogCritical(ex, msg, args);

    public IDisposable BeginScope<TState>(TState state) where TState : notnull => logger.BeginScope(state)!;
}
