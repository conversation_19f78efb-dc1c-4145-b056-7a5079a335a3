using Microsoft.Extensions.Logging;
using AwhGameServer.Application.Abstractions.Repositories;
using AwhGameServer.Application.Abstractions.UnitOfWork;
using AwhGameServer.Infrastructure.Persistence.Ef;

namespace AwhGameServer.Infrastructure.UnitOfWork;

/// <summary>
/// Реализация единицы работы для операций аутентификации.
/// Объединяет репозитории для работы с данными аутентификации и обеспечивает транзакционность операций.
/// </summary>
/// 
/// <param name="usersDataDbContext">Контекст базы пользовательских данных.</param>
/// <param name="authIdentityRepository">Репозиторий для работы с точками входа.</param>
/// <param name="authMethodsConfigRepository">Репозиторий для чтения конфигурации методов аутентификации.</param>
/// <param name="logger">Логгер для записи информации о работе единицы работы.</param>
public class AuthenticationUow(
    UsersDataDbContext usersDataDbContext,
    IAuthIdentityRepository authIdentityRepository,
    IAuthMethodsConfigReadRepository authMethodsConfigRepository,
    ILogger<AuthenticationUow> logger)
    : IAuthenticationUow
{
    /// <inheritdoc />
    public IAuthIdentityRepository AuthIdentityRepository { get; } = authIdentityRepository;

    /// <inheritdoc />
    public IAuthMethodsConfigReadRepository AuthMethodsConfigRepository { get; } = authMethodsConfigRepository;

    /// <inheritdoc />
    public async Task SaveChangesAsync(CancellationToken ct = default)
    {
        logger.LogDebug("Начало сохранения изменений в базе данных");

        try
        {
            var changesCount = await usersDataDbContext.SaveChangesAsync(ct);

            logger.LogInformation("Изменения успешно сохранены в базе данных. Количество изменений: {ChangesCount}", changesCount);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Ошибка при сохранении изменений в базе данных");
            throw;
        }
    }
}
