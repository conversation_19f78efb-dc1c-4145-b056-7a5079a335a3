using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using AwhGameServer.Domain.Aggregates.Game;
using AwhGameServer.Domain.Entities.Game;
using AwhGameServer.Application.Abstractions.Repositories;
using AwhGameServer.Infrastructure.Persistence.Ef;
using AwhGameServer.Infrastructure.Persistence.Ef.Models.GameData;

namespace AwhGameServer.Infrastructure.Repositories;

/// <summary>
/// Реализация репозитория для чтения конфигурации методов аутентификации.
/// Обеспечивает доступ к данным <see cref="AuthMethodsConfig"/> через Entity Framework и MongoDB.
/// </summary>
/// 
/// <param name="context">Контекст базы игровых данных.</param>
/// <param name="logger">Логгер для записи информации о работе репозитория.</param>
public class AuthMethodsConfigReadRepository(
    GameDataDbContext context, 
    ILogger<AuthMethodsConfigReadRepository> logger) 
    : IAuthMethodsConfigReadRepository
{
    /// <inheritdoc />
    public async Task<AuthMethodsConfig> GetConfigAsync(CancellationToken cancellationToken = default)
    {
        logger.LogDebug("Загрузка конфигурации методов аутентификации");

        var documents = await context.AuthMethods.ToListAsync(cancellationToken);

        var authMethods = documents.Select(MapToDomain).ToList();

        logger.LogInformation("Загружена конфигурация с {Count} методами аутентификации: {Methods}",
            authMethods.Count, string.Join(", ", authMethods.Select(m => m.MethodKey)));

        return new AuthMethodsConfig(authMethods);
    }

    /// <summary>
    /// Преобразует документ MongoDB в доменную модель.
    /// </summary>
    /// 
    /// <param name="document">Документ из базы данных.</param>
    /// 
    /// <returns>Доменная модель метода аутентификации.</returns>
    private static AuthMethod MapToDomain(AuthMethodDocument document)
    {
        return new AuthMethod(
            document.MethodKey,
            document.IsRegistrationAllowed,
            document.IsLoginAllowed
        );
    }
}
