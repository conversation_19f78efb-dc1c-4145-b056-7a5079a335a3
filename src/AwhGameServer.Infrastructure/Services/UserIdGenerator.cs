using Microsoft.Extensions.Options;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using AwhGameServer.Domain.ValueObjects;
using AwhGameServer.Application.Abstractions.Services;
using AwhGameServer.Infrastructure.Configurations;
using AwhGameServer.Infrastructure.Persistence.Ef.Models.UsersData;

namespace AwhGameServer.Infrastructure.Services;

/// <summary>
/// Генератор структурированных идентификаторов пользователей в формате SGNNNNNNNN.
/// </summary>
///
/// <typeparam name="T">Тип идентификатора, наследующий от TypedId</typeparam>
/// 
/// <param name="config">Конфигурация генератора с кодом сервера, поколением и ключом счетчика</param>
/// <param name="mongoClient">Клиент MongoDB для работы с базой данных</param>
/// <param name="usersDataDbConfig">Конфигурация базы пользовательских данных</param>
/// <param name="logger">Логгер для записи информации о работе генератора</param>
/// 
/// <remarks>
/// Создает уникальные идентификаторы пользователей со следующей структурой:
/// <list type="bullet">
/// <item><description>S — код сервера (1 цифра, 1-9)</description></item>
/// <item><description>G — поколение пользователей (1 цифра, 0-9)</description></item>
/// <item><description>NNNNNNNN — порядковый номер (8 цифр с ведущими нулями)</description></item>
/// </list>
///
/// Пример сгенерированных ID: 7100000001, 7100000002, 7100000003...
///
/// Использует атомарные операции MongoDB для обеспечения thread-safety
/// и предотвращения дублирования идентификаторов в многопоточной среде.
/// </remarks>
public class UserIdGenerator<T>(
    IOptions<UserIdGeneratorConfig> config,
    IMongoClient mongoClient,
    IOptions<UsersDataDbConfig> usersDataDbConfig,
    ILogger<UserIdGenerator<T>> logger)
    : ITypedIdGenerator<T> where T : TypedId
{
    private readonly UserIdGeneratorConfig _config = config.Value;
    private readonly IMongoCollection<CounterDocument> _countersCollection =
        mongoClient.GetDatabase(usersDataDbConfig.Value.DatabaseName).GetCollection<CounterDocument>("counters");

    /// <summary>
    /// Генерирует новый уникальный идентификатор пользователя.
    /// </summary>
    /// 
    /// <returns>
    /// Новый идентификатор в формате SGNNNNNNNN, где:
    /// S - код сервера, G - поколение, NNNNNNNN - порядковый номер.
    /// </returns>
    public async Task<T> New()
    {
        logger.LogDebug("Генерация нового ID пользователя. Сервер: {ServerCode}, Поколение: {UserGeneration}",
            _config.ServerCode, _config.UserGeneration);

        var nextNumber = await GetNextCounterValue();

        var userId = $"{_config.ServerCode}{_config.UserGeneration}{nextNumber:D8}";

        var typedId = (T)Activator.CreateInstance(typeof(T), userId)!;

        logger.LogInformation("Сгенерирован новый ID пользователя: {UserId} (счетчик: {Counter})",
            userId, nextNumber);

        return typedId;
    }

    /// <summary>
    /// Получает следующее значение счетчика с использованием атомарной операции MongoDB.
    /// </summary>
    /// 
    /// <remarks>
    /// Использует FindOneAndUpdate с опциями Upsert и ReturnDocument.After
    /// для обеспечения thread-safety и предотвращения race conditions.
    /// Если счетчик не существует, он будет создан автоматически.
    /// </remarks>
    /// 
    /// <returns>Следующее значение счетчика</returns>
    private async Task<long> GetNextCounterValue()
    {
        logger.LogDebug("Получение следующего значения счетчика для ключа: {CounterKey}", _config.CounterKey);

        var filter = Builders<CounterDocument>.Filter.Eq(x => x.CounterKey, _config.CounterKey);
        var update = Builders<CounterDocument>.Update.Inc(x => x.Value, 1);
        var options = new FindOneAndUpdateOptions<CounterDocument>
        {
            IsUpsert = true,
            ReturnDocument = ReturnDocument.After
        };

        var result = await _countersCollection.FindOneAndUpdateAsync(filter, update, options);

        logger.LogDebug("Получено значение счетчика: {CounterValue} для ключа: {CounterKey}",
            result.Value, _config.CounterKey);

        return result.Value;
    }
}
