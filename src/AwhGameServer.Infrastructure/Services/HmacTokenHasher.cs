using System.Security.Cryptography;
using System.Text;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Logging;
using AwhGameServer.Application.Abstractions.Models;
using AwhGameServer.Application.Abstractions.Services;
using AwhGameServer.Infrastructure.Configurations;

namespace AwhGameServer.Infrastructure.Services;

/// <summary>
/// Реализация <see cref="ITokenHasher"/> с использованием алгоритма HMAC-SHA256.
/// </summary>
///
/// <param name="config">Конфигурация HMAC-хешера токенов.</param>
/// <param name="logger">Логгер для записи информации о работе сервиса.</param>
/// 
/// <remarks>
/// Применяется для безопасного хранения токенов (например, refresh token) в виде хэш-суммы.
/// Вместо простого salt используется "pepper" — общий секрет, известный только серверу,
/// что защищает от атак при компрометации базы данных с хэшами.
/// Результат хэширования кодируется в URL-safe Base64-формате
/// (символы '+' заменяются на '-', '/' на '_', символы '=' удаляются).
/// </remarks>
public sealed class HmacTokenHasher(
    IOptions<HmacTokenHasherConfig> config, 
    ILogger<HmacTokenHasher> logger) 
    : ITokenHasher
{
    private readonly byte[] _pepper = Convert.FromBase64String(config.Value.PepperBase64);
    
    /// <summary>
    /// Хэширует указанный токен с использованием HMAC-SHA256 и pepper-секрета.
    /// </summary>
    /// 
    /// <param name="token">Исходный токен в виде строки.</param>
    /// <param name="cancellationToken">Токен отмены операции.</param>
    /// 
    /// <returns>Хэш токена в формате <see cref="TokenHash"/>.</returns>
    public Task<TokenHash> HashToken(string token, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(token);

        logger.LogDebug("Хэширование токена длиной {TokenLength} символов", token.Length);

        var data = Encoding.UTF8.GetBytes(token);

        using var hmac = new HMACSHA256(_pepper);

        var mac = hmac.ComputeHash(data);

        var tokenHash = new TokenHash(Base64UrlEncode(mac));

        logger.LogDebug("Токен успешно хэширован. Длина хэша: {HashLength}", tokenHash.HashBase64Url.Length);

        return Task.FromResult(tokenHash);
    }
    
    /// <summary>
    /// Преобразует массив байт в URL-safe Base64-строку.
    /// </summary>
    /// 
    /// <param name="bytes">Массив байт для кодирования.</param>
    /// 
    /// <returns>Base64-строка без символов '=', с заменой '+' на '-' и '/' на '_'.</returns>
    private static string Base64UrlEncode(byte[] bytes)
    {
        ArgumentNullException.ThrowIfNull(bytes);

        // Обычный Base64 → URL-safe: '+'→'-', '/'→'_', без '='
        var base64 = Convert.ToBase64String(bytes)
            .TrimEnd('=')
            .Replace('+', '-')
            .Replace('/', '_');
        
        return base64;
    }
}
