using System.ComponentModel.DataAnnotations;

namespace AwhGameServer.Infrastructure.Configurations;

/// <summary>
/// Конфигурация сессий аутентификации.
/// Содержит параметры для генерации access и refresh токенов.
/// </summary>
public class AuthSessionConfig
{
    /// <summary>
    /// Издатель токена доступа (issuer).
    /// </summary>
    [Required]
    public string AccessTokenIssuer { get; set; } = string.Empty;
    
    /// <summary>
    /// Аудитория токена доступа (audience).
    /// </summary>
    [Required]
    public string AccessTokenAudience { get; set; } = string.Empty;
    
    /// <summary>
    /// Время жизни токена доступа в минутах.
    /// </summary>
    [Required]
    public int AccessTokenLifetimeMinutes { get; set; } = 15;
    
    /// <summary>
    /// Секретный ключ для подписи токена доступа в формате Base64.
    /// </summary>
    [Required]
    [MinLength(64)]
    public string AccessTokenSecretBase64 { get; set; } = string.Empty;
    
    /// <summary>
    /// Время жизни refresh токена в днях.
    /// </summary>
    [Required]
    public int RefreshTokenLifetimeDays { get; set; } = 30;
}
