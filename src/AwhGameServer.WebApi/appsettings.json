{"Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.AspNetCore.Hosting": "Information"}}, "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId", "WithProcessId"], "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "Seq", "Args": {}}], "Properties": {"Application": "AwhGameServer"}}, "AllowedHosts": "*", "AuthSessionConfig": {"AccessTokenIssuer": "AwhGameServer", "AccessTokenAudience": "AwhGameClient", "AccessTokenLifetimeMinutes": 15, "RefreshTokenLifetimeDays": 30}, "UserIdGeneratorConfig": {"ServerCode": 3, "UserGeneration": 2, "CounterKey": "user_id_counter"}, "PersistenceConfig": {"MongoDbConnectionString": "mongodb://localhost:27017", "GameDataDbConfig": {"DatabaseName": "game_data", "GameDataDbSeedJsonPath": "./seed/GameDataDbSeed.json"}, "UsersDataDbConfig": {"DatabaseName": "users_data"}}}