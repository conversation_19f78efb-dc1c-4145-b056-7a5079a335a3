using Serilog;
using AwhGameServer.WebApi.Middleware;

namespace AwhGameServer.WebApi.Extensions;

/// <summary>
/// Расширения для настройки конвейера обработки запросов Web API.
/// </summary>
public static class WebApiWebApplicationExtensions
{
    /// <summary>
    /// Настраивает конвейер обработки запросов для Web API.
    /// </summary>
    public static WebApplication UseWebApi(this WebApplication app)
    {
        app.UseLogging();
        
        app.MapControllers();
        
        return app;
    }
    
    /// <summary>
    /// Добавляет логирование в конвейер обработки запросов.
    /// </summary>
    private static WebApplication UseLogging(this WebApplication app)
    {
        app.UseMiddleware<UserEnricherMiddleware>(); 
        app.UseSerilogRequestLogging();
        
        return app;
    }
}
