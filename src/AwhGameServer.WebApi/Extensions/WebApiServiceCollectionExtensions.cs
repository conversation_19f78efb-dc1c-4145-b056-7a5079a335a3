using System.Reflection;
using FluentValidation;
using Serilog;
using SharpGrip.FluentValidation.AutoValidation.Mvc.Extensions;

namespace AwhGameServer.WebApi.Extensions;

/// <summary>
/// Расширения для регистрации сервисов Web API в контейнере зависимостей.
/// </summary>
public static class WebApiServiceCollectionExtensions
{
    /// <summary>
    /// Регистрирует сервисы Web API в контейнере зависимостей.
    /// </summary>
    public static IServiceCollection AddWebApi(
        this IServiceCollection services, 
        IConfiguration configuration,
        IHostBuilder host)
    {
        services.AddLogging(configuration, host);
        
        services.AddControllers();

        services.AddRequestValidations();

        return services;
    }
    
    /// <summary>
    /// Регистрирует логирование в контейнере зависимостей.
    /// </summary>
    private static IServiceCollection AddLogging(
        this IServiceCollection services,
        IConfiguration configuration,
        IHostBuilder host)
    {
        Log.Logger = new LoggerConfiguration()
            .ReadFrom.Configuration(configuration)
            .Enrich.FromLogContext()
            .CreateLogger();

        host.UseSerilog();
        
        return services;
    }
    
    /// <summary>
    /// Регистрирует валидаторы запросов в контейнере зависимостей.
    /// </summary>
    private static IServiceCollection AddRequestValidations(this IServiceCollection services)
    {
        services.AddValidatorsFromAssembly(Assembly.GetCallingAssembly());
        services.AddFluentValidationAutoValidation();

        return services;
    }
}
