using Serilog.Context;

namespace AwhGameServer.WebApi.Middleware;

/// <summary>
/// Сервис для добавления информации о пользователе в контекст логирования.
/// </summary>
public sealed class UserEnricherMiddleware(RequestDelegate next)
{
    public async Task Invoke(HttpContext ctx)
    {
        var userId =
            ctx.User?.FindFirst("sub")?.Value
            ?? "anonymous";

        using (LogContext.PushProperty("UserId", userId))
        {
            await next(ctx);
        }
    }
}
