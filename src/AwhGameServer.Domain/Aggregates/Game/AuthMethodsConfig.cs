using AwhGameServer.Domain.Abstractions;
using AwhGameServer.Domain.Entities.Game;

namespace AwhGameServer.Domain.Aggregates.Game;

/// <summary>
/// Хранит список доступных методов аутентификации и предоставляет способы проверки их разрешений.
/// </summary>
/// 
/// <param name="allowedAuthMethods">Коллекция методов аутентификации, доступных в системе.</param>
public class AuthMethodsConfig(IEnumerable<AuthMethod> allowedAuthMethods) : IAggregateRoot
{
    private readonly List<AuthMethod> _authMethods = allowedAuthMethods.ToList();

    /// <summary>
    /// Список доступных методов аутентификации.
    /// </summary>
    public IReadOnlyList<AuthMethod> AllowedAuthMethods => _authMethods;

    /// <summary>
    /// Определяет, разрешена ли регистрация через указанный метод аутентификации.
    /// Если метод отсутствует в списке, возвращает <see langword="false"/>.
    /// </summary>
    /// 
    /// <param name="methodKey">Ключ метода аутентификации (чувствителен к регистру).</param>
    /// 
    /// <returns><see langword="true"/>, если регистрация через метод разрешена; иначе <see langword="false"/>.</returns>
    public bool IsRegistrationMethodAllowed(string methodKey)
    {
        var authMethod = _authMethods.FirstOrDefault(x => x.MethodKey == methodKey);
        return authMethod?.IsRegistrationAllowed ?? false;
    }

    /// <summary>
    /// Определяет, разрешён ли вход через указанный метод аутентификации.
    /// Возвращает <see langword="false"/>, если ключ пустой или метод отсутствует в списке.
    /// </summary>
    /// 
    /// <param name="methodKey">Ключ метода аутентификации (чувствителен к регистру).</param>
    /// 
    /// <returns><see langword="true"/>, если вход через метод разрешён; иначе <see langword="false"/>.</returns>
    public bool IsLoginMethodAllowed(string methodKey)
    {
        if (string.IsNullOrWhiteSpace(methodKey)) 
            return false;

        var authMethod = _authMethods.FirstOrDefault(x => x.MethodKey == methodKey);
        return authMethod?.IsLoginAllowed ?? false;
    }
}
