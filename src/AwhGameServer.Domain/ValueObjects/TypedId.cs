using AwhGameServer.Domain.Exceptions;

namespace AwhGameServer.Domain.ValueObjects;

/// <summary>
/// Базовый Value Object для идентификаторов сущностей домена.
/// </summary>
///
/// <exception cref="DomainException">Выбрасывается, если значение <paramref name="Value"/> равно <see langword="null"/> или пустой строке.</exception>
/// 
/// <remarks>
/// Используется для строгой типизации идентификаторов различных сущностей, 
/// чтобы избежать ошибок, связанных с передачей идентификаторов между разными типами,
/// а также для защиты инварианта "ID не может быть null или пустым".
/// <para>
/// Идентификатор хранится в виде строки и должен удовлетворять следующим условиям:
/// <list type="bullet">
/// <item><description>Не может быть <see langword="null"/> или пустой строкой.</description></item>
/// <item><description>Должен быть уникальным в пределах своей сущности (контролируется вне данного класса).</description></item>
/// </list>
/// </para>
/// <para>
/// Наследники определяют конкретный тип идентификатора, например:
/// <code>
/// public sealed record UserId(string Value) : TypedId(Value);
/// public sealed record WalletId(string Value) : TypedId(Value);
/// </code>
/// </para>
/// </remarks>
/// <param name="Value">Строковое представление идентификатора.</param>
public abstract record TypedId(string Value)
{
    public string Value { get; } =
        string.IsNullOrWhiteSpace(Value)
            ? throw new DomainException("Value cannot be null or empty")
            : Value;

    public sealed override string ToString() => Value;
}
