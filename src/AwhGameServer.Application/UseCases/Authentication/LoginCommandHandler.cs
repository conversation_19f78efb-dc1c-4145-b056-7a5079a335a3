using AwhGameServer.Domain.Aggregates.Game;
using AwhGameServer.Domain.Aggregates.Users;
using AwhGameServer.Domain.ValueObjects.Users;
using AwhGameServer.Application.Abstractions.Messaging;
using AwhGameServer.Application.Abstractions.UnitOfWork;
using AwhGameServer.Application.Abstractions.Services;
using AwhGameServer.Application.Abstractions.Stores;
using AwhGameServer.Application.Abstractions.Models;
using AwhGameServer.Application.Abstractions.Logging;

using AwhGameServer.Application.Dto;
using AwhGameServer.Application.Exceptions;

namespace AwhGameServer.Application.UseCases.Authentication;

/// <summary>
/// Обработчик команды авторизации пользователя.
/// </summary>
/// 
/// <remarks>
/// Реализует сценарий входа в систему с поддержкой гостевых аккаунтов и их последующей привязки
/// к постоянным методам аутентификации (Google, Apple, соцсети и т.д.).
/// 
/// Логика работы:
/// <list type="number">
/// <item>
/// Если предоставлена не-гостевая точка входа — авторизация выполняется через неё (приоритетнее гостевой).
/// При отсутствии пользователя, но наличии гостевой точки входа — новая точка входа привязывается к существующему пользователю.
/// </item>
/// <item>
/// Если предоставлена только гостевая точка входа — выполняется авторизация или создаётся новый пользователь.
/// </item>
/// <item>
/// Если точек входа нет или они конфликтуют (несколько гостевых или несколько не-гостевых) — выбрасывается исключение.
/// </item>
/// </list>
/// 
/// После успешной аутентификации:
/// <list type="bullet">
/// <item>Генерируется access/refresh токен.</item>
/// <item>Создаётся новая сессия, при этом все предыдущие сессии пользователя аннулируются.</item>
/// </list>
/// 
/// Проверка доступности методов аутентификации выполняется через <see cref="AuthMethodsConfig"/>.
/// </remarks>
public class LoginCommandHandler(
    IAuthenticationUow uow,
    ITypedIdGenerator<UserId> userIdGenerator,
    ITypedIdGenerator<AuthIdentityId> authIdentityIdGenerator,
    IAuthTokensGenerator authTokensGenerator,
    ITokenHasher tokenHasher,
    IAuthSessionStore authSessionStore,
    IAppLogger<LoginCommandHandler> logger)
    : ICommandHandler<LoginCommand, LoginCommandResult>
{
    private const string GuestAuthMethodKey = "Guest";
    
    public async Task<LoginCommandResult> Handle(LoginCommand command, CancellationToken ct)
    {
        using var scope = logger.BeginScope("LoginCommand");
        logger.Info("Начало обработки команды входа с {AuthIdentitiesCount} точками входа", command.AuthIdentities.Count);
        
        if (command.AuthIdentities.Count == 0)
        {
            logger.Warn("Попытка входа с пустой коллекцией AuthIdentities");
            throw new ApplicationArgumentException("AuthIdentities cannot be empty", nameof(command.AuthIdentities));
        }

        var nonGuestAuthIdentityDto = GetNonGuestIdentity(command);
        var guestAuthIdentityDto = GetGuestIdentity(command);

        logger.Debug("Определены точки входа: NonGuest={NonGuestMethod}, Guest={GuestMethod}",
            nonGuestAuthIdentityDto?.AuthMethod ?? "null",
            guestAuthIdentityDto?.AuthMethod ?? "null");

        var authMethodsConfig = await uow.AuthMethodsConfigRepository.GetConfigAsync(ct);

        (AuthIdentity authIdentity, bool isNewUser) authPipelineResult;

        if (nonGuestAuthIdentityDto is not null) // Если есть не_гостевая точка входа, пытаемся авторизоваться через неё (приоритетнее)
        {
            logger.Info("Выполняется аутентификация через не-гостевую точку входа: {AuthMethod}", nonGuestAuthIdentityDto.AuthMethod);
            authPipelineResult = await AuthenticateNonGuest(nonGuestAuthIdentityDto, guestAuthIdentityDto, authMethodsConfig, ct);
        }
        else if (guestAuthIdentityDto is not null) // Если есть только гостевая точка входа, пытаемся авторизоваться через неё
        {
            logger.Info("Выполняется аутентификация через гостевую точку входа");
            authPipelineResult = await AuthenticateGuest(guestAuthIdentityDto, authMethodsConfig, ct);
        }
        else
        {
            // Если нет ни гостевой, ни не_гостевой точки входа, то выбрасываем исключение (ситуация не должна быть возможной)
            var exception = new ApplicationArgumentException("No valid identities provided", nameof(command.AuthIdentities));
            logger.Error(exception, "Не найдено валидных точек входа");
            throw exception;
        }

        await uow.SaveChangesAsync(ct);
        logger.Debug("Изменения сохранены в базе данных");

        var authenticationResult = await Authenticate(authPipelineResult.authIdentity, ct);

        var authTokensDto = new AuthTokensDto(
            authenticationResult.authTokens.AccessToken,
            authenticationResult.authTokens.AccessTokenExpiresAtUtc,
            authenticationResult.authTokens.RefreshToken,
            authenticationResult.authTokens.RefreshTokenExpiresAtUtc);

        logger.Info("Успешная аутентификация пользователя {UserId}, новый пользователь: {IsNewUser}, сессия: {SessionId}",
            authPipelineResult.authIdentity.UserId.Value,
            authPipelineResult.isNewUser,
            authenticationResult.sessionId);

        return new LoginCommandResult(
            authPipelineResult.authIdentity.UserId.Value,
            authPipelineResult.isNewUser,
            authenticationResult.sessionId,
            authenticationResult.authTokens.RefreshTokenExpiresAtUtc,
            authTokensDto);
    }
    
    private static AuthIdentityDto? GetNonGuestIdentity(LoginCommand command)
    { 
        var nonGuestIdentities = command.AuthIdentities
            .Where(x => x.AuthMethod != GuestAuthMethodKey)
            .ToList();
        
        //Проверка конфликта нескольких точек входа
        if (nonGuestIdentities.Count > 1)
            throw new ApplicationArgumentException("Multiple non-guest identities are not allowed", nameof(command.AuthIdentities));
        
        return nonGuestIdentities.FirstOrDefault();
    }
    
    private static AuthIdentityDto? GetGuestIdentity(LoginCommand command)
    {
        var guestIdentities = command.AuthIdentities
            .Where(x => x.AuthMethod == GuestAuthMethodKey)
            .ToList();
        
        //Проверка конфликта нескольких точек входа
        if (guestIdentities.Count > 1)
            throw new ApplicationArgumentException("Multiple guest identities are not allowed", nameof(command.AuthIdentities));
        
        return guestIdentities.FirstOrDefault();
    }
    
    // Пайплайн авторизации через не_гостевую точку входа
    private async Task<(AuthIdentity authIdentity, bool isNewUser)> AuthenticateNonGuest(
        AuthIdentityDto nonGuestAuthIdentityDto,
        AuthIdentityDto? guestAuthIdentityDto,
        AuthMethodsConfig authMethodsConfig,
        CancellationToken ct)
    {
        logger.Debug("Проверка разрешения входа для метода {AuthMethod}", nonGuestAuthIdentityDto.AuthMethod);
        var isLoginAllowed = authMethodsConfig.IsLoginMethodAllowed(nonGuestAuthIdentityDto.AuthMethod);

        if (!isLoginAllowed)
        {
            logger.Warn("Вход через {AuthMethod} запрещен конфигурацией", nonGuestAuthIdentityDto.AuthMethod);
            throw new AuthenticationException($"Login with {nonGuestAuthIdentityDto.AuthMethod} is not allowed");
        }

        logger.Debug("Поиск существующей точки входа для метода {AuthMethod}", nonGuestAuthIdentityDto.AuthMethod);
        var userAuthIdentity = await uow.AuthIdentityRepository
            .GetByAuthMethodAndTokenAsync(nonGuestAuthIdentityDto.AuthMethod, nonGuestAuthIdentityDto.AuthToken, ct);

        if (userAuthIdentity is not null)
        {
            // Если пользователь уже есть, то авторизуем его
            logger.Info("Найдена существующая точка входа для пользователя {UserId}", userAuthIdentity.UserId.Value);
            return (userAuthIdentity, false);
        }
        
        // Если пользователя нет, то проверяем, есть ли гостевая точка входа
        logger.Debug("Точка входа не найдена, проверка наличия гостевой точки входа");
        if (guestAuthIdentityDto is not null)
        {
            var guestAuthIdentity = await uow.AuthIdentityRepository
                .GetByAuthMethodAndTokenAsync(guestAuthIdentityDto.AuthMethod, guestAuthIdentityDto.AuthToken, ct);

            if (guestAuthIdentity is not null)
            {
                // Если есть гостевая точка входа, но нет не_гостевой, то привязываем к нему новую точку входа
                logger.Info("Привязка не-гостевой точки входа {AuthMethod} к существующему гостевому пользователю {UserId}",
                    nonGuestAuthIdentityDto.AuthMethod, guestAuthIdentity.UserId.Value);
                userAuthIdentity = await CreateNewAuthIdentity(guestAuthIdentity.UserId, nonGuestAuthIdentityDto, authMethodsConfig, ct);
            }
        }

        if (userAuthIdentity is not null)
        {
            // Если есть гостевая точка входа, то авторизуем его (с новой не_гостевой точкой входа)
            logger.Info("Успешная привязка не-гостевой точки входа к пользователю {UserId}", userAuthIdentity.UserId.Value);
            return (userAuthIdentity, false);
        }

        // Если нет гостевой точки входа, то создаём нового пользователя
        logger.Info("Создание нового пользователя с не-гостевой точкой входа {AuthMethod}", nonGuestAuthIdentityDto.AuthMethod);
        userAuthIdentity = await CreateNewUser(nonGuestAuthIdentityDto, authMethodsConfig, ct);

        // Авторизуем нового пользователя
        return (userAuthIdentity, true);
    }
    
    // Пайплайн авторизации через гостевую точку входа
    private async Task<(AuthIdentity authIdentity, bool isNewUser)> AuthenticateGuest(
        AuthIdentityDto guestIdentity,
        AuthMethodsConfig authMethodsConfig,
        CancellationToken ct)
    {
        logger.Debug("Проверка разрешения гостевого входа");
        var isLoginAllowed = authMethodsConfig.IsLoginMethodAllowed(GuestAuthMethodKey);

        if (!isLoginAllowed)
        {
            logger.Warn("Гостевой вход запрещен конфигурацией");
            throw new AuthenticationException($"Login with {GuestAuthMethodKey} is not allowed");
        }

        logger.Debug("Поиск существующей гостевой точки входа");
        var authIdentity = await uow.AuthIdentityRepository
            .GetByAuthMethodAndTokenAsync(guestIdentity.AuthMethod, guestIdentity.AuthToken, ct);

        // Если гостевая точка входа не найдена, то создаём нового пользователя
        var isNewUser = authIdentity is null;
        if (isNewUser)
        {
            logger.Info("Гостевая точка входа не найдена, создание нового гостевого пользователя");
            authIdentity = await CreateNewUser(guestIdentity, authMethodsConfig, ct);
        }
        else
        {
            logger.Info("Найдена существующая гостевая точка входа для пользователя {UserId}", authIdentity!.UserId.Value);
        }

        return (authIdentity, isNewUser);
    }

    private async Task<AuthIdentity> CreateNewUser(
        AuthIdentityDto authIdentityDto,
        AuthMethodsConfig authMethodsConfig,
        CancellationToken ct)
    {
        logger.Debug("Генерация нового UserId");
        var userId = await userIdGenerator.New();
        
        logger.Info("Создание нового пользователя {UserId} с методом {AuthMethod}", userId.Value, authIdentityDto.AuthMethod);
        return await CreateNewAuthIdentity(userId, authIdentityDto, authMethodsConfig, ct);
    }

    private async Task<AuthIdentity> CreateNewAuthIdentity(
        UserId userId,
        AuthIdentityDto authIdentityDto,
        AuthMethodsConfig authMethodsConfig,
        CancellationToken ct)
    {
        logger.Debug("Проверка разрешения регистрации для метода {AuthMethod}", authIdentityDto.AuthMethod);
        var isRegistrationAllowed = authMethodsConfig.IsRegistrationMethodAllowed(authIdentityDto.AuthMethod);

        if (!isRegistrationAllowed)
        {
            logger.Warn("Регистрация через {AuthMethod} запрещена конфигурацией", authIdentityDto.AuthMethod);
            throw new AuthenticationException($"Registration with {authIdentityDto.AuthMethod} is not allowed");
        }

        logger.Debug("Генерация нового AuthIdentityId");
        var authIdentityId = await authIdentityIdGenerator.New();

        var authIdentity = new AuthIdentity(authIdentityId, userId, authIdentityDto.AuthToken, authIdentityDto.AuthMethod);

        logger.Debug("Добавление новой точки входа {AuthIdentityId} для пользователя {UserId}", authIdentityId.Value, userId.Value);
        await uow.AuthIdentityRepository.AddAsync(authIdentity, ct);

        return authIdentity;
    }

    private async Task<(string sessionId, DateTime sessionExpiresAtUtc, AuthTokens authTokens)> Authenticate(AuthIdentity authIdentity, CancellationToken ct)
    {
        logger.Debug("Начало создания сессии для пользователя {UserId}", authIdentity.UserId.Value);
        var sessionId = Guid.NewGuid().ToString();

        logger.Debug("Генерация токенов для сессии {SessionId}", sessionId);
        var authTokens = await authTokensGenerator.GenerateAuthTokens(sessionId, authIdentity.UserId, ct);

        logger.Debug("Хеширование refresh token");
        var refreshHash = await tokenHasher.HashToken(authTokens.RefreshToken, ct);

        // Создаем сессию на то же время, что и refresh token
        var sessionExpiresAtUtc = authTokens.RefreshTokenExpiresAtUtc;

        logger.Info("Создание новой сессии {SessionId} для пользователя {UserId}, истекает {ExpiresAt}",
            sessionId, authIdentity.UserId.Value, sessionExpiresAtUtc);
        await authSessionStore.RevokeAllUserSessionsThenCreateAsync(
            sessionId,
            authIdentity.UserId,
            refreshHash,
            authIdentity.Id,
            sessionExpiresAtUtc,
            ct);

        logger.Debug("Сессия {SessionId} успешно создана", sessionId);
        return (sessionId, sessionExpiresAtUtc, authTokens);
    }
}
