namespace AwhGameServer.Application.Exceptions;

public class ApplicationArgumentException : ArgumentException
{
    public ApplicationArgumentException(string message) : base(message) { }
    public ApplicationArgumentException(string message, Exception innerException) : base(message, innerException) { }
    public ApplicationArgumentException(string message, string paramName) : base(message, paramName) { }
    public ApplicationArgumentException(string message, string paramName, Exception innerException) : base(message, paramName, innerException) { }
}
