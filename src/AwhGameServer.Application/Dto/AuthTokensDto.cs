namespace AwhGameServer.Application.Dto;

/// <summary>
/// DTO для передачи данных о токенах аутентификации.
/// </summary>
/// 
/// <param name="AccessToken">JWT токен доступа. Не может быть пустым.</param>
/// <param name="RefreshToken">Refresh токен. Не может быть пустым.</param>
public record AuthTokensDto(
    string AccessToken,
    DateTime AccessTokenExpiresAtUtc,
    string RefreshToken,
    DateTime RefreshTokenExpiresAtUtc
    );
