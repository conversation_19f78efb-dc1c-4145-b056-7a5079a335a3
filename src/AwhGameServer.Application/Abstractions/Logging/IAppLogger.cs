namespace AwhGameServer.Application.Abstractions.Logging;

/// <summary>
/// Интерфейс для логирования слоя приложения.
/// </summary>
/// <typeparam name="T"></typeparam>
public interface IAppLogger<T>
{
    void Trace(string messageTemplate, params object?[] args);
    void Debug(string messageTemplate, params object?[] args);
    void Info(string messageTemplate, params object?[] args);
    void Warn(string messageTemplate, params object?[] args);
    void Error(Exception exception, string messageTemplate, params object?[] args);
    void Critical(Exception exception, string messageTemplate, params object?[] args);

    IDisposable BeginScope<TState>(TState state);
}
