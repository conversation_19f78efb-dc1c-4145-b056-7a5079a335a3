namespace AwhGameServer.Application.Abstractions.Messaging;

/// <summary>
/// Контракт обработчика команды в паттерне CQRS.
/// </summary>
/// 
/// <typeparam name="TCommand">Тип команды, которую обрабатывает данный обработчик.</typeparam>
/// <typeparam name="TResponse">Тип результата, возвращаемого после выполнения команды.</typeparam>
/// 
/// <remarks>
/// Обработчик инкапсулирует логику изменения состояния системы,
/// не выполняя напрямую бизнес-логику сущностей, а координируя их через слой Application.
/// </remarks>
public interface ICommandHandler<in TCommand, TResponse>
    where TCommand : ICommand<TResponse>
{
    /// <summary>
    /// Выполняет указанную команду.
    /// </summary>
    /// 
    /// <param name="command">Команда, которую необходимо выполнить.</param>
    /// <param name="ct">Токен отмены.</param>
    /// 
    /// <returns>Результат выполнения команды.</returns>
    Task<TResponse> Handle(TCommand command, CancellationToken ct);
}
