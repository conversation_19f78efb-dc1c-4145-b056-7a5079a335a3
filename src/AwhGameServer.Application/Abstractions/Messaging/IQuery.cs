namespace AwhGameServer.Application.Abstractions.Messaging;

/// <summary>
/// Маркерный интерфейс запроса в паттерне CQRS.
/// </summary>
/// 
/// <typeparam name="TResponse">Тип результата, возвращаемого после выполнения запроса.</typeparam>
/// 
/// <remarks>
/// Запросы не изменяют состояние системы.
/// Используется вместе с <see cref="IQueryHandler{TQuery,TResponse}"/> и медиатором (например, MediatR).
/// </remarks>
public interface IQuery<out TResponse>;
