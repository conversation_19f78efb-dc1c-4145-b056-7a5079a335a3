namespace AwhGameServer.Contracts.Api.V3.Authentication;

/// <summary>
/// Контрактная модель запроса на обновление токенов доступа.
/// Используется для получения новых токенов без повторной аутентификации.
/// </summary>
public class RefreshRequest
{
    /// <summary>
    /// Токен обновления для получения новых токенов доступа.
    /// Должен быть действительным и не истекшим.
    /// </summary>
    public string RefreshToken { get; set; } = null!;
}
