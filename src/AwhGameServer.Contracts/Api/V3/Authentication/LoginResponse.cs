using AwhGameServer.Contracts.Api.V3.Models;

namespace AwhGameServer.Contracts.Api.V3.Authentication;

/// <summary>
/// Контрактная модель ответа на запрос входа в систему.
/// Содержит токены аутентификации для успешно аутентифицированного пользователя.
/// </summary>
public class LoginResponse
{
    /// <summary>
    /// Токены аутентификации, выданные пользователю после успешного входа.
    /// Включают токен доступа и токен обновления.
    /// </summary>
    public AuthTokens AuthTokens { get; set; } = null!;
}
