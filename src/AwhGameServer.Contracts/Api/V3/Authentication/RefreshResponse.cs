using AwhGameServer.Contracts.Api.V3.Models;

namespace AwhGameServer.Contracts.Api.V3.Authentication;

/// <summary>
/// Контрактная модель ответа на запрос обновления токенов доступа.
/// Содержит новые токены аутентификации для пользователя.
/// </summary>
public class RefreshResponse
{
    /// <summary>
    /// Новые токены аутентификации, выданные пользователю после обновления.
    /// Включают новый токен доступа и новый токен обновления.
    /// </summary>
    public AuthTokens AuthTokens { get; set; } = null!;
}
