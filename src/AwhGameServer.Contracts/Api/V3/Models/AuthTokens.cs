namespace AwhGameServer.Contracts.Api.V3.Models;

/// <summary>
/// Контрактная модель токенов аутентификации.
/// Содержит пару токенов для доступа к API и обновления сессии.
/// </summary>
public class AuthTokens
{
    /// <summary>
    /// Токен доступа для авторизации API запросов.
    /// Имеет ограниченное время жизни и используется для аутентификации пользователя.
    /// </summary>
    public string AccessToken { get; set; } = null!;

    /// <summary>
    /// Токен обновления для получения новых токенов доступа.
    /// Имеет более длительное время жизни и используется для обновления сессии.
    /// </summary>
    public string RefreshToken { get; set; } = null!;
}
